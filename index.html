<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Creative Hydraulics - Premium Hydraulic Solutions</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            600: '#2563eb',
                            700: '#1d4ed8',
                        },
                        purple: {
                            600: '#9333ea',
                            700: '#7c3aed',
                        }
                    }
                }
            }
        }
    </script>
    <link rel="stylesheet" href="style.css">
  
</head>
<body class="min-h-screen bg-white">
    <!-- Header -->
    <header class="sticky top-0 z-50 border-b bg-white/95 backdrop-blur-sm">
        <div class="container mx-auto px-4">
            <div class="flex h-16 items-center justify-between">
                <div class="flex items-center space-x-2">
                    <div class="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-r from-blue-600 to-purple-600">
                        <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 2.69l5.66 5.66a8 8 0 11-11.32 0z"/>
                        </svg>
                    </div>
                    <span class="text-xl font-bold text-gray-900">Creative Hydraulics</span>
                </div>

                <nav class="hidden md:flex items-center space-x-8" id="desktop-nav">
                    <a href="#about" class="text-gray-600 hover:text-gray-900 transition-colors">About Us</a>
                    <a href="#products" class="text-gray-600 hover:text-gray-900 transition-colors">Products</a>
                    <a href="#clients" class="text-gray-600 hover:text-gray-900 transition-colors">Clients</a>
                    <a href="#quotation" class="text-gray-600 hover:text-gray-900 transition-colors">Get Quote</a>
                    <a href="#contact" class="text-gray-600 hover:text-gray-900 transition-colors">Contact</a>
                </nav>

                <div class="hidden md:flex items-center space-x-4">
                    <button class="px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">Get Quote</button>
                    <button class="px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-lg transition-all">Contact Us</button>
                </div>

                <button class="md:hidden" id="mobile-menu-btn">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" id="menu-icon">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                    </svg>
                    <svg class="h-6 w-6 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24" id="close-icon">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>

            <!-- Mobile Menu -->
            <div class="md:hidden border-t bg-white py-4 hidden" id="mobile-menu">
                <nav class="flex flex-col space-y-4">
                    <a href="#about" class="text-gray-600 hover:text-gray-900">About Us</a>
                    <a href="#products" class="text-gray-600 hover:text-gray-900">Products</a>
                    <a href="#clients" class="text-gray-600 hover:text-gray-900">Clients</a>
                    <a href="#quotation" class="text-gray-600 hover:text-gray-900">Get Quote</a>
                    <a href="#contact" class="text-gray-600 hover:text-gray-900">Contact</a>
                    <div class="flex flex-col space-y-2 pt-4">
                        <button class="px-4 py-2 text-left text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg">Get Quote</button>
                        <button class="px-4 py-2 text-left bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg">Contact Us</button>
                    </div>
                </nav>
            </div>
        </div>
    </header>

    <main>
        <!-- Hero Section -->
        <section class="relative overflow-hidden bg-gradient-to-br from-gray-50 to-white py-20 border-b-8 border-transparent animate-border-gradient shadow-xl rounded-b-3xl">
            <div class="container mx-auto px-4 text-center">
                <div class="mx-auto max-w-4xl">
                    <div id="hero-slider" class="relative">
                        <!-- Slide 1 -->
                        <div class="hero-slide active" data-slide="0">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-50 text-blue-700 mb-4 animate-fadeUp" style="animation-delay:0.2s">🔧 Premium Hydraulic Solutions</span>
                            <h1 class="mb-6 text-6xl font-bold tracking-tight text-gray-900 sm:text-5xl lg:text-7xl animate-fadeUp" style="animation-delay:0.4s; animation-fill-mode: both;">
                                CREATIVE 
                                <span class="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent shimmer">
                                   HYDRAULICS
                                </span>
                            </h1>
                            <p class="mb-8 text-xl text-gray-600 max-w-2xl mx-auto animate-fadeUp" style="animation-delay:0.6s; animation-fill-mode: both;">
                                Leading manufacturer of high-performance hydraulic components and systems. Delivering precision,
                                reliability, and innovation for industrial applications worldwide.
                            </p>
                            <div class="flex justify-center mb-8">
                                <svg width="120" height="60" viewBox="0 0 120 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <rect x="10" y="25" width="40" height="10" rx="5" fill="#2563eb"/>
                                    <rect x="70" y="25" width="40" height="10" rx="5" fill="#9333ea"/>
                                    <rect x="50" y="28" width="20" height="4" rx="2" fill="#1d4ed8"/>
                                    <circle cx="20" cy="30" r="6" fill="#dbeafe" stroke="#2563eb" stroke-width="2"/>
                                    <circle cx="100" cy="30" r="6" fill="#ede9fe" stroke="#9333ea" stroke-width="2"/>
                                    <rect x="58" y="18" width="4" height="24" rx="2" fill="#7c3aed"/>
                                </svg>
                            </div>
                            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                                <button class="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-lg font-semibold flex items-center justify-center">
                                    Get Quote
                                    <svg class="ml-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14m-7-7l7 7-7 7"/>
                                    </svg>
                                </button>
                                <button class="px-6 py-3 bg-white text-gray-900 border border-gray-300 hover:bg-gray-50 rounded-lg font-semibold">
                                    View Products
                                </button>
                            </div>
                        </div>

                        <!-- Slide 2 -->
                        <div class="hero-slide flex flex-col md:flex-row items-center justify-center gap-8 md:gap-20" data-slide="1">
                            <div class="flex-1 text-center md:text-left">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-base font-semibold bg-blue-50 text-blue-700 mb-4">
                                    🛠️ System Monitoring
                                </span>
                                <h1 class="mb-6 text-3xl font-extrabold tracking-tight text-gray-900 sm:text-4xl lg:text-5xl leading-tight">
                                    Monitor & Control Your
                                    <span class="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                                        Hydraulic Systems
                                    </span>
                                </h1>
                                <p class="mb-8 text-lg text-gray-700 max-w-xl mx-auto md:mx-0 font-medium">
                                    Get real-time insights into pressure, flow, and performance. Our advanced dashboards help you optimize efficiency, prevent downtime, and ensure safety across all your hydraulic equipment.
                                </p>
                            </div>
                            <div class="flex-1 flex justify-center">
                                <img src="cylinder.jpg" alt="Hydraulic System Dashboard" class="rounded-2xl shadow-2xl w-[200px] h-[150px] md:w-[400px] md:h-[300px] lg:w-[400px] lg:h-[300px] object-cover border-4 border-blue-100">
                            </div>
                        </div>

                        <!-- Slide 3 -->
                        <div class="hero-slide flex flex-col md:flex-row items-center justify-center gap-8" data-slide="2">
                            <div class="flex-1 text-center md:text-left">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-50 text-blue-700 mb-4">
                                    🎥 Live Demo
                                </span>
                                <h1 class="mb-6 text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl lg:text-6xl">
                                    See StreamLine in Action,
                                    <span class="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                                        Experience the Power
                                    </span>
                                </h1>
                                <p class="mb-8 text-xl text-gray-600 max-w-2xl mx-auto md:mx-0">
                                    Watch how StreamLine transforms complex workflows into simple, automated processes. See real examples and discover how it can revolutionize your business operations.
                                </p>
                             
                            </div>
                            <div class="flex-1 flex justify-center mt-12 md:mt-0">
                                <div class="mx-auto max-w-2xl w-full">
                                    <div class="relative aspect-video rounded-lg border-2 border-dashed border-blue-300 bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center cursor-pointer hover:border-blue-500 transition-colors">
                                        <div class="text-center">
                                            <p class="text-gray-600 font-medium">
                                                Click to Play Demo Video
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Slider Navigation Dots -->
                    <div class="flex justify-center gap-3 mt-12">
                        <button class="nav-dot active h-3 w-3 rounded-full bg-blue-600 scale-125 transition-all" data-slide="0"></button>
                        <button class="nav-dot h-3 w-3 rounded-full bg-gray-300 hover:bg-gray-400 transition-all" data-slide="1"></button>
                        <button class="nav-dot h-3 w-3 rounded-full bg-gray-300 hover:bg-gray-400 transition-all" data-slide="2"></button>
                    </div>
                </div>
            </div>
        </section>

        <!-- About Section -->
        <section id="about" class="py-20 bg-white">
            <div class="container mx-auto px-4">
                <!-- Header -->
                <div class="text-center mb-16">
                    <div class="inline-block px-4 py-2 bg-red-50 text-red-600 text-sm font-semibold rounded-full mb-4 tracking-wide">
                        ABOUT US
                    </div>
                    <h2 class="text-4xl sm:text-5xl font-bold text-gray-900 mb-6 tracking-tight">
                        About Creative Hydraulics
                    </h2>
                    <div class="w-16 h-1 bg-red-500 mx-auto mb-6"></div>
                    <p class="text-lg text-gray-600 max-w-4xl mx-auto leading-relaxed">
                        Delivering excellence in hydraulic manufacturing since inception, with
                        a commitment to innovation and quality.
                    </p>
                </div>

                <!-- Main Content -->
                <div class="flex flex-col lg:flex-row items-start gap-16">
                    
                        <!-- Leadership Quote -->
                        <div class="bg-gray-50 p-6 rounded-lg border-l-4 border-blue-500">
                            <p class="text-gray-700 italic mb-4 text-lg">
                                "Our mission is to deliver world-class hydraulic solutions with unmatched reliability,
                                innovation, and support to our global clientele."
                            </p>
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold text-lg mr-4">
                                    BT
                                </div>
                                <div>
                                    <p class="font-semibold text-gray-900">Basavaraj B. Tonannavar</p>
                                    <p class="text-sm text-gray-600">Managing Director</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Right Image -->
                    <div class="flex-1 lg:pl-8">
                        <div class="relative">
                            <div class="bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-8 shadow-xl">
                                <img src="/assets/i" alt="Basavaraj B. Tonannavar - Managing Director"
                                     class="w-full h-auto rounded-xl shadow-lg object-cover">
                                <div class="absolute bottom-4 left-4 right-4 bg-white/90 backdrop-blur-sm rounded-lg p-4">
                                    <h4 class="font-bold text-gray-900 text-lg">Basavaraj B. Tonannavar</h4>
                                    <p class="text-blue-600 font-medium">Managing Director</p>
                                </div>
                            </div>

                            <!-- Decorative elements -->
                            <div class="absolute -top-4 -right-4 w-20 h-20 bg-blue-100 rounded-full opacity-60"></div>
                            <div class="absolute -bottom-4 -left-4 w-16 h-16 bg-purple-100 rounded-full opacity-60"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Products Section -->
        <section id="products" class="py-24 bg-gradient-to-br from-blue-50 via-white to-purple-50 relative shadow-lg rounded-3xl mx-2 md:mx-0 mb-8">
            <div class="container mx-auto px-4">
                <div class="text-center mb-12">
                    <h2 class="text-4xl sm:text-5xl font-extrabold text-gray-900 mb-3 tracking-tight font-sans">Our <span class="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Products</span></h2>
                    <div class="flex justify-center mt-2 mb-2">
                        <span class="inline-block px-4 py-1 rounded-full bg-blue-100 text-blue-700 text-sm font-semibold tracking-wide shadow">Innovative Hydraulic Solutions</span>
                    </div>
                    <p class="text-xl text-gray-700 max-w-2xl mx-auto font-medium">Premium quality, engineered for performance and reliability</p>
                </div>
                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-10">
    <div class="bg-white border border-gray-200 rounded-lg shadow-lg hover:shadow-xl transition-shadow p-6 text-center">
        <img src="cylinder.jpg" alt="Hydraulic Cylinders" class="w-full h-48 object-cover rounded-lg mb-4">
        <h3 class="text-xl font-semibold text-gray-900 mb-2">Hydraulic Cylinders</h3>
        <p class="text-gray-600 mb-4">High-performance cylinders designed for demanding applications</p>
        <button class="px-4 py-2 bg-white text-gray-900 border border-gray-300 hover:bg-gray-50 rounded-lg font-semibold">
            Learn More
        </button>
    </div>
    <div class="bg-white border border-gray-200 rounded-lg shadow-lg hover:shadow-xl transition-shadow p-6 text-center">
        <img src="hydrulic pump.jpg" alt="Hydraulic Pumps" class="w-full h-48 object-cover rounded-lg mb-4">
        <h3 class="text-xl font-semibold text-gray-900 mb-2">Hydraulic Pumps</h3>
        <p class="text-gray-600 mb-4">Efficient and reliable pumps for various industrial uses</p>
        <button class="px-4 py-2 bg-white text-gray-900 border border-gray-300 hover:bg-gray-50 rounded-lg font-semibold">
            Learn More
        </button>
    </div>
    <div class="bg-white border border-gray-200 rounded-lg shadow-lg hover:shadow-xl transition-shadow p-6 text-center">
        <img src="control valve.jpg" alt="Control Valves" class="w-full h-48 object-cover rounded-lg mb-4">
        <h3 class="text-xl font-semibold text-gray-900 mb-2">Control Valves</h3>
        <p class="text-gray-600 mb-4">Precision-engineered valves for optimal flow control</p>
        <button class="px-4 py-2 bg-white text-gray-900 border border-gray-300 hover:bg-gray-50 rounded-lg font-semibold">
            Learn More
        </button>
    </div>
</div>
        </section>

        <!-- Clients Section -->
        <section id="clients" class="py-20 bg-gray-100 shadow-lg rounded-3xl mx-2 md:mx-0 mb-8">
            <div class="container mx-auto px-4">
                <div class="text-center mb-16">
                    <h2 class="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">Our Clients</h2>
                    <p class="text-xl text-gray-600 max-w-2xl mx-auto">Trusted by Industry Leaders Worldwide</p>
                </div>

                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8 items-center">
                    <div class="flex justify-center">
                        <img src="/placeholder.svg?height=60&width=120&text=Client+1" alt="Industrial Client 1" class="grayscale hover:grayscale-0 transition-all duration-300 opacity-70 hover:opacity-100 max-w-full h-auto">
                    </div>
                    <div class="flex justify-center">
                        <img src="/placeholder.svg?height=60&width=120&text=Client+2" alt="Industrial Client 2" class="grayscale hover:grayscale-0 transition-all duration-300 opacity-70 hover:opacity-100 max-w-full h-auto">
                    </div>
                    <div class="flex justify-center">
                        <img src="/placeholder.svg?height=60&width=120&text=Client+3" alt="Industrial Client 3" class="grayscale hover:grayscale-0 transition-all duration-300 opacity-70 hover:opacity-100 max-w-full h-auto">
                    </div>
                    <div class="flex justify-center">
                        <img src="/placeholder.svg?height=60&width=120&text=Client+4" alt="Industrial Client 4" class="grayscale hover:grayscale-0 transition-all duration-300 opacity-70 hover:opacity-100 max-w-full h-auto">
                    </div>
                    <div class="flex justify-center">
                        <img src="/placeholder.svg?height=60&width=120&text=Client+5" alt="Industrial Client 5" class="grayscale hover:grayscale-0 transition-all duration-300 opacity-70 hover:opacity-100 max-w-full h-auto">
                    </div>
                    <div class="flex justify-center">
                        <img src="/placeholder.svg?height=60&width=120&text=Client+6" alt="Industrial Client 6" class="grayscale hover:grayscale-0 transition-all duration-300 opacity-70 hover:opacity-100 max-w-full h-auto">
                    </div>
                </div>
            </div>
        </section>

        <!-- Testimonials Section -->
        <section id="testimonials" class="py-20 bg-white shadow-lg rounded-3xl mx-2 md:mx-0 mb-8">
            <div class="container mx-auto px-4">
                <div class="text-center mb-16">
                    <h2 class="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">Trusted by Industry Leaders</h2>
                    <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                        See what our customers have to say about Creative Hydraulics
                    </p>
                </div>

                <div class="grid md:grid-cols-3 gap-8">
                    <div class="bg-white border border-gray-200 rounded-lg shadow-lg p-6">
                        <div class="flex mb-4">
                            <svg class="h-5 w-5 text-yellow-400 fill-current" viewBox="0 0 24 24">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                            <svg class="h-5 w-5 text-yellow-400 fill-current" viewBox="0 0 24 24">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                            <svg class="h-5 w-5 text-yellow-400 fill-current" viewBox="0 0 24 24">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                            <svg class="h-5 w-5 text-yellow-400 fill-current" viewBox="0 0 24 24">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                            <svg class="h-5 w-5 text-yellow-400 fill-current" viewBox="0 0 24 24">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                        </div>
                        <p class="text-gray-600 mb-6 italic">
                            "Creative Hydraulics has revolutionized our manufacturing process. Their hydraulic systems are incredibly reliable and have reduced our downtime by 80%."
                        </p>
                        <div class="flex items-center">
                            <img src="/placeholder.svg?height=40&width=40&text=SJ" alt="Sarah Johnson" class="w-10 h-10 rounded-full mr-3">
                            <div>
                                <p class="font-semibold text-gray-900">Sarah Johnson</p>
                                <p class="text-sm text-gray-600">CEO, TechCorp</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white border border-gray-200 rounded-lg shadow-lg p-6">
                        <div class="flex mb-4">
                            <svg class="h-5 w-5 text-yellow-400 fill-current" viewBox="0 0 24 24">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                            <svg class="h-5 w-5 text-yellow-400 fill-current" viewBox="0 0 24 24">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                            <svg class="h-5 w-5 text-yellow-400 fill-current" viewBox="0 0 24 24">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                            <svg class="h-5 w-5 text-yellow-400 fill-current" viewBox="0 0 24 24">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                            <svg class="h-5 w-5 text-yellow-400 fill-current" viewBox="0 0 24 24">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                        </div>
                        <p class="text-gray-600 mb-6 italic">
                            "The precision and quality of their hydraulic components are unmatched. We've been working with them for over 15 years and they never disappoint."
                        </p>
                        <div class="flex items-center">
                            <img src="/placeholder.svg?height=40&width=40&text=MC" alt="Michael Chen" class="w-10 h-10 rounded-full mr-3">
                            <div>
                                <p class="font-semibold text-gray-900">Michael Chen</p>
                                <p class="text-sm text-gray-600">CTO, InnovateLab</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white border border-gray-200 rounded-lg shadow-lg p-6">
                        <div class="flex mb-4">
                            <svg class="h-5 w-5 text-yellow-400 fill-current" viewBox="0 0 24 24">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                            <svg class="h-5 w-5 text-yellow-400 fill-current" viewBox="0 0 24 24">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                            <svg class="h-5 w-5 text-yellow-400 fill-current" viewBox="0 0 24 24">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                            <svg class="h-5 w-5 text-yellow-400 fill-current" viewBox="0 0 24 24">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                            <svg class="h-5 w-5 text-yellow-400 fill-current" viewBox="0 0 24 24">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                        </div>
                        <p class="text-gray-600 mb-6 italic">
                            "Outstanding support and engineering expertise. Creative Hydraulics helped us design a custom solution that perfectly fits our unique requirements."
                        </p>
                        <div class="flex items-center">
                            <img src="/placeholder.svg?height=40&width=40&text=ER" alt="Emily Rodriguez" class="w-10 h-10 rounded-full mr-3">
                            <div>
                                <p class="font-semibold text-gray-900">Emily Rodriguez</p>
                                <p class="text-sm text-gray-600">Operations Director, GrowthCo</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>



        <!-- Enhanced Quotation Section -->
        <section id="quotation" class="py-20 bg-gradient-to-br from-blue-50 to-purple-50 shadow-lg rounded-3xl mx-2 md:mx-0 mb-8">
            <div class="container mx-auto px-4">
                <div class="text-center mb-16">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-700 mb-4">
                        💼  Quotation Service
                    </span>
                    <h2 class="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">Request a Professional Quotation</h2>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                        Partner with Creative Hydraulics for precision-engineered solutions. Our expert team will provide a
                        detailed, customized quote within 24 hours, backed by 30+ years of hydraulic engineering excellence.
                    </p>
                </div>

                <div class="grid lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
                    <!-- Quote Form -->
                    <div class="lg:col-span-2">
                        <div class="bg-white rounded-lg shadow-xl border-0 overflow-hidden">
                            <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
                                <h3 class="flex items-center text-2xl font-bold">
                                    <svg class="mr-3 h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 01-2-2V5a2 2 0 012-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                                    </svg>
                                    Custom Quote Request
                                </h3>
                                <p class="text-blue-100 mt-2">Complete the form below for a detailed, professional quotation</p>
                            </div>
                            <div class="p-8">
                                <form id="quote-form" class="space-y-6">
                                    <!-- Contact Information -->
                                    <div class="grid md:grid-cols-2 gap-6">
                                        <div class="space-y-2">
                                            <label for="name" class="block text-sm font-semibold text-gray-700">
                                                Full Name <span class="text-red-500">*</span>
                                            </label>
                                            <input type="text" id="name" name="name" required class="w-full h-11 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                        </div>
                                        <div class="space-y-2">
                                            <label for="company" class="block text-sm font-semibold text-gray-700">
                                                Company/Organization
                                            </label>
                                            <input type="text" id="company" name="company" class="w-full h-11 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                        </div>
                                    </div>

                                    <div class="grid md:grid-cols-2 gap-6">
                                        <div class="space-y-2">
                                            <label for="email" class="block text-sm font-semibold text-gray-700">
                                                Email Address <span class="text-red-500">*</span>
                                            </label>
                                            <input type="email" id="email" name="email" required class="w-full h-11 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                        </div>
                                        <div class="space-y-2">
                                            <label for="phone" class="block text-sm font-semibold text-gray-700">
                                                Phone Number
                                            </label>
                                            <input type="tel" id="phone" name="phone" class="w-full h-11 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                        </div>
                                    </div>

                                    <!-- Product Selection -->
                                    <div class="grid md:grid-cols-2 gap-6">
                                        <div class="space-y-2">
                                            <label for="product" class="block text-sm font-semibold text-gray-700">
                                                Product Category <span class="text-red-500">*</span>
                                            </label>
                                            <select id="product" name="product" required class="w-full h-11 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                                <option value="">Select a product category</option>
                                                <option value="hydraulic-cylinders">Hydraulic Cylinders</option>
                                                <option value="hydraulic-pumps">Hydraulic Pumps</option>
                                                <option value="control-valves">Control Valves</option>
                                                <option value="hydraulic-motors">Hydraulic Motors</option>
                                                <option value="filtration-systems">Filtration Systems</option>
                                                <option value="complete-systems">Complete Hydraulic Systems</option>
                                                <option value="custom-solution">Custom Engineering Solution</option>
                                            </select>
                                        </div>
                                        <div class="space-y-2">
                                            <label for="application" class="block text-sm font-semibold text-gray-700">
                                                Application Type
                                            </label>
                                            <select id="application" name="application" class="w-full h-11 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                                <option value="">Select application type</option>
                                                <option value="industrial-automation">Industrial Automation</option>
                                                <option value="construction-equipment">Construction Equipment</option>
                                                <option value="agricultural-machinery">Agricultural Machinery</option>
                                                <option value="marine-applications">Marine Applications</option>
                                                <option value="aerospace">Aerospace</option>
                                                <option value="mining-equipment">Mining Equipment</option>
                                                <option value="manufacturing">Manufacturing</option>
                                                <option value="other">Other</option>
                                            </select>
                                        </div>
                                        
                                    </div>

                                    <!-- Technical Specifications -->
                                    <div class="bg-gray-50 p-6 rounded-lg">
                                        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                            <svg class="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                            </svg>
                                            Technical Specifications
                                        </h3>
                                        <div class="grid md:grid-cols-3 gap-4">
                                            <div class="space-y-2">
                                                <label for="quantity" class="block text-sm font-semibold text-gray-700">
                                                    Quantity Required
                                                </label>
                                                <input type="text" id="quantity" name="quantity" placeholder="e.g., 10 units" class="w-full h-11 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                            </div>
                                            <div class="space-y-2">
                                                <label for="pressure" class="block text-sm font-semibold text-gray-700">
                                                    Operating Pressure
                                                </label>
                                                <input type="text" id="pressure" name="pressure" placeholder="e.g., 3000 PSI" class="w-full h-11 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                            </div>
                                            <div class="space-y-2">
                                                <label for="flowRate" class="block text-sm font-semibold text-gray-700">
                                                    Flow Rate
                                                </label>
                                                <input type="text" id="flowRate" name="flowRate" placeholder="e.g., 50 GPM" class="w-full h-11 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                            </div>
                                        </div>
                                        <div class="grid md:grid-cols-2 gap-4 mt-4">
                                            <div class="space-y-2">
                                                <label for="operatingTemp" class="block text-sm font-semibold text-gray-700">
                                                    Operating Temperature Range
                                                </label>
                                                <input type="text" id="operatingTemp" name="operatingTemp" placeholder="e.g., -20°C to +80°C" class="w-full h-11 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                            </div>
                                            <div class="space-y-2">
                                                <label for="timeline" class="block text-sm font-semibold text-gray-700">
                                                    Required Timeline
                                                </label>
                                                <select id="timeline" name="timeline" class="w-full h-11 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                                    <option value="">Select timeline</option>
                                                    <option value="immediate">Immediate (Rush Order)</option>
                                                    <option value="1-2-weeks">1-2 Weeks</option>
                                                    <option value="3-4-weeks">3-4 Weeks</option>
                                                    <option value="1-2-months">1-2 Months</option>
                                                    <option value="3-6-months">3-6 Months</option>
                                                    <option value="flexible">Flexible</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Project Details -->
                                    <div class="space-y-2">
                                        <label for="details" class="block text-sm font-semibold text-gray-700">
                                            Project Details & Special Requirements <span class="text-red-500">*</span>
                                        </label>
                                        <textarea id="details" name="details" rows="5" required placeholder="Please provide detailed information about your project requirements, environmental conditions, mounting specifications, certifications needed, etc." class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"></textarea>
                                    </div>

                                    <!-- Budget Range -->
                                    <div class="space-y-2">
                                        <label for="budget" class="block text-sm font-semibold text-gray-700">
                                            Budget Range (Optional)
                                        </label>
                                        <select id="budget" name="budget" class="w-full h-11 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                            <option value="">Select budget range</option>
                                            <option value="under-10k">Under $10,000</option>
                                            <option value="10k-25k">$10,000 - $25,000</option>
                                            <option value="25k-50k">$25,000 - $50,000</option>
                                            <option value="50k-100k">$50,000 - $100,000</option>
                                            <option value="100k-250k">$100,000 - $250,000</option>
                                            <option value="over-250k">Over $250,000</option>
                                            <option value="discuss">Prefer to Discuss</option>
                                        </select>
                                    </div>

                                    <div class="bg-blue-50 p-4 rounded-lg flex items-start space-x-3">
                                        <svg class="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                                        </svg>
                                        <p class="text-sm text-blue-800">
                                            <strong>Confidentiality Guaranteed:</strong> All information provided is kept strictly
                                            confidential. We provide fully customized, industry-compliant solutions with complete technical
                                            documentation.
                                        </p>
                                    </div>

                                    <button type="submit" class="w-full h-12 text-lg bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-lg font-semibold flex items-center justify-center transition-all">
                                        <svg class="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                        </svg>
                                        Request Professional Quote
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Info Panel -->
                    <div class="space-y-6">
                        <!-- Why Choose Us -->
                        <div class="bg-white rounded-lg shadow-lg border-0 overflow-hidden">
                            <div class="bg-gradient-to-r from-green-600 to-blue-600 text-white p-6">
                                <h3 class="flex items-center text-xl font-bold">
                                    <svg class="mr-2 h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 2.69l5.66 5.66a8 8 0 11-11.32 0z"/>
                                    </svg>
                                    Why Choose Creative Hydraulics?
                                </h3>
                            </div>
                            <div class="p-6">
                                <ul class="space-y-4">
                                    <li class="flex items-start space-x-3">
                                        <svg class="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                        </svg>
                                        <span class="text-sm text-gray-700">ISO 9001:2015 Certified Manufacturing</span>
                                    </li>
                                    <li class="flex items-start space-x-3">
                                        <svg class="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                        </svg>
                                        <span class="text-sm text-gray-700">30+ Years Industry Experience</span>
                                    </li>
                                    <li class="flex items-start space-x-3">
                                        <svg class="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                        </svg>
                                        <span class="text-sm text-gray-700">24-Hour Quote Response Time</span>
                                    </li>
                                    <li class="flex items-start space-x-3">
                                        <svg class="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                        </svg>
                                        <span class="text-sm text-gray-700">Custom Engineering & OEM Solutions</span>
                                    </li>
                                    <li class="flex items-start space-x-3">
                                        <svg class="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                        </svg>
                                        <span class="text-sm text-gray-700">Global Shipping & Local Support</span>
                                    </li>
                                    <li class="flex items-start space-x-3">
                                        <svg class="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                        </svg>
                                        <span class="text-sm text-gray-700">Comprehensive Technical Documentation</span>
                                    </li>
                                    <li class="flex items-start space-x-3">
                                        <svg class="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                        </svg>
                                        <span class="text-sm text-gray-700">Quality Assurance & Testing</span>
                                    </li>
                                    <li class="flex items-start space-x-3">
                                        <svg class="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                        </svg>
                                        <span class="text-sm text-gray-700">Competitive Pricing & Flexible Terms</span>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <!-- Contact Information -->
                        <div class="bg-white rounded-lg shadow-lg border-0">
                            <div class="p-6 border-b">
                                <h3 class="text-lg font-bold">Direct Contact</h3>
                            </div>
                            <div class="p-6 space-y-4">
                                <div class="flex items-center space-x-3">
                                    <svg class="h-5 w-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                                    </svg>
                                    <div>
                                        <p class="font-semibold text-gray-900">Quote Hotline</p>
                                        <a href="tel:+15551234567" class="text-blue-600 hover:underline">
                                            +****************
                                        </a>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <svg class="h-5 w-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                                    </svg>
                                    <div>
                                        <p class="font-semibold text-gray-900">Quote Email</p>
                                        <a href="mailto:<EMAIL>" class="text-blue-600 hover:underline">
                                            <EMAIL>
                                        </a>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <svg class="h-5 w-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                    <div>
                                        <p class="font-semibold text-gray-900">Response Time</p>
                                        <p class="text-gray-600">Within 24 hours</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Certifications -->
                        <div class="bg-white rounded-lg shadow-lg border-0">
                            <div class="p-6 border-b">
                                <h3 class="flex items-center text-lg font-bold">
                                    <svg class="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 10-.806 1.946 3.42 3.42 0 00-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-1.946-.806 3.42 3.42 0 00-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 10.806-1.946 3.42 3.42 0 003.138-3.138z"/>
                                    </svg>
                                    Certifications & Standards
                                </h3>
                            </div>
                            <div class="p-6">
                                <div class="grid grid-cols-2 gap-4">
                                    <div class="text-center p-3 bg-gray-50 rounded-lg">
                                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                                            <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 10-.806 1.946 3.42 3.42 0 00-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-1.946-.806 3.42 3.42 0 00-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 10.806-1.946 3.42 3.42 0 003.138-3.138z"/>
                                            </svg>
                                        </div>
                                        <div class="text-xs text-gray-700 font-semibold">ISO 9001:2015</div>
                                    </div>
                                    <div class="text-center p-3 bg-gray-50 rounded-lg">
                                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                                            <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 10-.806 1.946 3.42 3.42 0 00-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-1.946-.806 3.42 3.42 0 00-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 10.806-1.946 3.42 3.42 0 003.138-3.138z"/>
                                            </svg>
                                        </div>
                                        <div class="text-xs text-gray-700 font-semibold">NFPA Certified</div>
                                    </div>
                                    <div class="text-center p-3 bg-gray-50 rounded-lg">
                                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                                            <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 10-.806 1.946 3.42 3.42 0 00-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-1.946-.806 3.42 3.42 0 00-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 10.806-1.946 3.42 3.42 0 003.138-3.138z"/>
                                            </svg>
                                        </div>
                                        <div class="text-xs text-gray-700 font-semibold">CE Marking</div>
                                    </div>
                                    <div class="text-center p-3 bg-gray-50 rounded-lg">
                                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                                            <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 10-.806 1.946 3.42 3.42 0 00-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-1.946-.806 3.42 3.42 0 00-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 10.806-1.946 3.42 3.42 0 003.138-3.138z"/>
                                            </svg>
                                        </div>
                                        <div class="text-xs text-gray-700 font-semibold">ROHS Compliant</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        
        <section id="contact" class="py-24 bg-gradient-to-br from-white via-blue-50 to-purple-50 shadow-xl rounded-3xl mx-2 md:mx-0 mb-12 relative z-10">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="text-4xl sm:text-5xl font-extrabold text-gray-900 mb-3 tracking-tight">Contact Us</h2>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto">Reach out to our team for any inquiries or support.</p>
        </div>
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
            <div class="bg-white/90 border border-blue-100 rounded-2xl shadow-2xl p-10 flex flex-col justify-center">
                <h3 class="text-2xl font-bold mb-6 text-blue-700">Send us a Message</h3>
                <form id="contact-form" class="space-y-6">
                    <div class="space-y-2">
                        <label for="contact-name" class="block text-base font-semibold text-gray-700">Name</label>
                        <input type="text" id="contact-name" name="contact-name" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/80">
                    </div>
                    <div class="space-y-2">
                        <label for="contact-email" class="block text-base font-semibold text-gray-700">Email</label>
                        <input type="email" id="contact-email" name="contact-email" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/80">
                    </div>
                    <div class="space-y-2">
                        <label for="contact-message" class="block text-base font-semibold text-gray-700">Message</label>
                        <textarea id="contact-message" name="contact-message" rows="4" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/80"></textarea>
                    </div>
                    <button type="submit" class="w-full px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-lg font-semibold text-lg shadow-md transition-all">
                        Send Message
                    </button>
                </form>
            </div>
            <div class="space-y-8 flex flex-col justify-center">
                <div class="overflow-hidden rounded-2xl shadow-xl border border-blue-100">
                    <iframe
                        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3024.123456789!2d-74.0059413!3d40.7127753!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zNDDCsDQyJzQ2LjAiTiA3NMKwMDAnMjEuNCJX!5e0!3m2!1sen!2sus!4v1234567890"
                        width="100%"
                        height="300"
                        style="border: 0; min-width: 100%; min-height: 300px; display: block;"
                        allowfullscreen
                        loading="lazy"
                        referrerpolicy="no-referrer-when-downgrade">
                    </iframe>
                    <p class="text-center text-base text-gray-700 mt-2 font-medium">
                        <strong>Creative Hydraulics HQ</strong><br />123 Industrial Avenue, Engineering Park, Tech City
                    </p>
                </div>
                <div class="space-y-6">
                    <div class="flex items-start space-x-4">
                        <svg class="h-6 w-6 text-blue-600 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                        </svg>
                        <div>
                            <h3 class="font-semibold text-gray-900">Address</h3>
                            <p class="text-gray-600">123 Industrial Avenue<br />Engineering Park, Tech City</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-4">
                        <svg class="h-6 w-6 text-blue-600 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                        </svg>
                        <div>
                            <h3 class="font-semibold text-gray-900">Phone</h3>
                            <p class="text-gray-600">+****************</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-4">
                        <svg class="h-6 w-6 text-blue-600 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                        </svg>
                        <div>
                            <h3 class="font-semibold text-gray-900">Email</h3>
                            <p class="text-gray-600"><EMAIL></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

        
   

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="container mx-auto px-4">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-r from-blue-600 to-purple-600">
                            <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 2.69l5.66 5.66a8 8 0 11-11.32 0z"/>
                            </svg>
                        </div>
                        <span class="text-xl font-bold">Creative Hydraulics</span>
                    </div>
                    <p class="text-gray-400 mb-4">
                        Precision hydraulic engineering and manufacturing for industrial, mobile, and custom applications. Trusted
                        worldwide for reliability and innovation.
                    </p>
                    <div class="flex space-x-4">
                        <img src="/placeholder.svg?height=32&width=32&text=ISO" alt="ISO Certified" class="w-8 h-8 opacity-70">
                        <img src="/placeholder.svg?height=32&width=32&text=FPS" alt="Fluid Power Society" class="w-8 h-8 opacity-70">
                    </div>
                </div>

                <div>
                    <h3 class="font-semibold mb-4">Solutions</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#products" class="hover:text-white transition-colors">Hydraulic Cylinders</a></li>
                        <li><a href="#products" class="hover:text-white transition-colors">Hydraulic Pumps</a></li>
                        <li><a href="#products" class="hover:text-white transition-colors">Control Valves</a></li>
                        <li><a href="#quotation" class="hover:text-white transition-colors">Request a Quote</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-semibold mb-4">Contact</h3>
                    <ul class="space-y-3 text-gray-400">
                        <li class="flex items-start space-x-2">
                            <svg class="h-4 w-4 mt-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                            <span class="text-sm">123 Industrial Avenue, Tech City</span>
                        </li>
                        <li class="flex items-center space-x-2">
                            <svg class="h-4 w-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                            </svg>
                            <span class="text-sm">+****************</span>
                        </li>
                        <li class="flex items-center space-x-2">
                            <svg class="h-4 w-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                            </svg>
                            <span class="text-sm"><EMAIL></span>
                        </li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-semibold mb-4">Industry</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white transition-colors">Fluid Power Resources</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Hydraulic Safety</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Case Studies</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Careers</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-8 pt-8 flex flex-col sm:flex-row justify-between items-center">
                <p class="text-gray-400 text-sm">© 2025 Creative Hydraulics. All rights reserved.</p>
                <div class="flex space-x-4 mt-4 sm:mt-0">
                    <a href="#" class="text-gray-400 hover:text-white transition-colors">
                        <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778 3.203 4.092v4.711zM5.005 6.575a1.548 1.548 0 11-.003-3.096 1.548 1.548 0 01.003 3.096zm-1.337 9.763H6.34v-8.59H3.667v8.59zM17.668 1H2.328C1.595 1 1 1.581 1 2.298v15.403C1 18.418 1.595 19 2.328 19h15.34c.734 0 1.332-.582 1.332-1.299V2.298C19 1.581 18.402 1 17.668 1z" clip-rule="evenodd"/>
                        </svg>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-white transition-colors">
                        <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M23.498 6.186a2.994 2.994 0 00-2.112-2.12C19.228 3.5 12 3.5 12 3.5s-7.228 0-9.386.566a2.994 2.994 0 00-2.112 2.12C0 8.355 0 12 0 12s0 3.645.502 5.814a2.994 2.994 0 002.112 2.12C4.772 20.5 12 20.5 12 20.5s7.228 0 9.386-.566a2.994 2.994 0 002.112-2.12C24 15.645 24 12 24 12s0-3.645-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
